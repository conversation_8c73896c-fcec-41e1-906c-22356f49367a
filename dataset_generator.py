#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Secluded语法数据集生成器
用于生成高质量的训练数据集，包含input、think、output三段格式
"""

import json
import random
import re
from typing import List, Dict, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatasetItem:
    """数据集项目结构"""
    input: str
    think: str
    output: str

class SecludedDatasetGenerator:
    """Secluded语法数据集生成器"""
    
    def __init__(self, source_file: str = "Secluded变量大全.txt"):
        self.source_file = source_file
        self.syntax_knowledge = self._load_syntax_knowledge()
        self.question_templates = self._init_question_templates()
        self.error_patterns = self._init_error_patterns()
        
    def _load_syntax_knowledge(self) -> Dict[str, Any]:
        """加载语法知识库"""
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        knowledge = {
            'basic_syntax': self._extract_basic_syntax(content),
            'variables': self._extract_variables(content),
            'functions': self._extract_functions(content),
            'control_flow': self._extract_control_flow(content),
            'examples': self._extract_examples(content)
        }
        return knowledge
    
    def _extract_basic_syntax(self, content: str) -> Dict[str, Any]:
        """提取基础语法"""
        return {
            'comments': ['//注释', '##注释', '&&注释'],
            'structure': {
                'head': '词汇头（正则表达式）',
                'body': '词汇体（回复内容）',
                'separator': '空行分隔'
            },
            'regex_support': 'ECMAScript正则表达式'
        }
    
    def _extract_variables(self, content: str) -> List[Dict[str, str]]:
        """提取变量信息"""
        variables = []
        # 提取 %变量名% 格式的变量
        var_pattern = r'%([^%]+)%'
        matches = re.findall(var_pattern, content)
        
        # 常用变量示例
        common_vars = [
            {'name': 'QQ', 'desc': '操作者QQ号', 'example': '%QQ%'},
            {'name': '昵称', 'desc': '操作者昵称', 'example': '%昵称%'},
            {'name': '群号', 'desc': '消息所在群聊号码', 'example': '%群号%'},
            {'name': 'MSG', 'desc': '消息内容', 'example': '%MSG%'},
            {'name': '时间戳秒', 'desc': '当前秒时间戳', 'example': '%时间戳秒%'},
            {'name': '随机数', 'desc': '随机数生成', 'example': '%随机数%'},
        ]
        return common_vars
    
    def _extract_functions(self, content: str) -> List[Dict[str, str]]:
        """提取函数信息"""
        functions = []
        # 提取 $函数名 参数$ 格式的函数
        func_pattern = r'\$([^$]+)\$'
        
        # 常用函数示例
        common_funcs = [
            {'name': '图片', 'syntax': '$图片 路径/链接$', 'desc': '发送图片'},
            {'name': '语音', 'syntax': '$语音 时长 路径$', 'desc': '发送语音'},
            {'name': '访问', 'syntax': '$访问 链接$', 'desc': 'HTTP请求'},
            {'name': '休眠', 'syntax': '$休眠 毫秒$', 'desc': '延时执行'},
            {'name': '循环', 'syntax': '$循环 行 次数$', 'desc': '循环执行'},
            {'name': '变量', 'syntax': '$变量 键 值$', 'desc': '创建变量'},
        ]
        return common_funcs
    
    def _extract_control_flow(self, content: str) -> List[Dict[str, str]]:
        """提取控制流信息"""
        return [
            {'type': '条件判断', 'syntax': 'if:条件\n内容\nelse\n内容', 'desc': '条件分支'},
            {'type': '中文条件', 'syntax': '如果:条件\n内容\n返回\n如果尾', 'desc': '中文条件判断'},
            {'type': '循环', 'syntax': '$循环 行 次数$', 'desc': '重复执行'},
            {'type': '跳转', 'syntax': '$跳转 行$', 'desc': '跳转到指定行'},
        ]
    
    def _extract_examples(self, content: str) -> List[Dict[str, str]]:
        """提取示例代码"""
        examples = []
        lines = content.split('\n')
        
        current_example = None
        for i, line in enumerate(lines):
            line = line.strip()
            if line and not line.startswith('//') and not line.startswith('##') and not line.startswith('&&'):
                if current_example is None:
                    current_example = {'head': line, 'body': []}
                elif line == '':
                    if current_example and current_example['body']:
                        examples.append(current_example)
                    current_example = None
                else:
                    if current_example:
                        current_example['body'].append(line)
        
        return examples[:20]  # 限制示例数量
    
    def _init_question_templates(self) -> List[Dict[str, str]]:
        """初始化问题模板"""
        return [
            # 基础问答类
            {
                'type': 'basic_qa',
                'templates': [
                    "帮我写一个简单的问答词条，当用户发送"{trigger}"时，机器人回复"{response}"",
                    "我想创建一个词汇，用户输入"{trigger}"，机器人回答"{response}"",
                    "写个基础的对话，用户说"{trigger}"，机器人说"{response}"",
                    "能帮我做个简单的问答吗？用户发"{trigger}"，回复"{response}"",
                ]
            },
            # 变量使用类
            {
                'type': 'variable_usage',
                'templates': [
                    "怎么在回复中显示用户的QQ号？",
                    "如何获取当前时间？",
                    "怎样在消息中包含用户昵称？",
                    "如何显示群号信息？",
                    "怎么获取随机数？",
                ]
            },
            # 函数使用类
            {
                'type': 'function_usage',
                'templates': [
                    "怎么发送图片？",
                    "如何发送语音消息？",
                    "怎样发起HTTP请求？",
                    "如何实现延时回复？",
                    "怎么创建自定义变量？",
                ]
            },
            # 语法解释类
            {
                'type': 'syntax_explanation',
                'templates': [
                    "Secluded的基本语法结构是什么？",
                    "词汇头和词汇体有什么区别？",
                    "如何写注释？",
                    "正则表达式怎么用？",
                    "条件判断怎么写？",
                ]
            },
            # 错误修正类
            {
                'type': 'error_correction',
                'templates': [
                    "这段代码有什么问题：{error_code}",
                    "帮我修复这个语法错误：{error_code}",
                    "为什么这个词汇不工作：{error_code}",
                    "这个写法对吗：{error_code}",
                ]
            },
            # 完整功能类
            {
                'type': 'complete_feature',
                'templates': [
                    "写一个完整的签到功能",
                    "创建一个天气查询词库",
                    "做一个简单的聊天机器人",
                    "写个群管理功能",
                    "创建一个音乐点播系统",
                ]
            }
        ]
    
    def _init_error_patterns(self) -> List[Dict[str, str]]:
        """初始化常见错误模式"""
        return [
            {
                'error': '你好\n\n你好呀！',
                'issue': '词汇头和词汇体之间有空行',
                'correct': '你好\n你好呀！'
            },
            {
                'error': '// 你好\n你好呀！',
                'issue': '词汇头被注释了',
                'correct': '你好\n你好呀！'
            },
            {
                'error': '%QQ\n你的QQ是%QQ%',
                'issue': '变量语法错误，缺少%',
                'correct': '%QQ%\n你的QQ是%QQ%'
            },
            {
                'error': '$图片 路径\n好的',
                'issue': '函数语法错误，缺少结束符$',
                'correct': '$图片 路径$\n好的'
            }
        ]
    
    def generate_basic_qa_data(self, count: int = 50) -> List[DatasetItem]:
        """生成基础问答数据"""
        data = []
        qa_pairs = [
            ("你好", "你好呀！"), ("再见", "再见！"), ("谢谢", "不客气~"),
            ("早上好", "早上好！"), ("晚安", "晚安，好梦~"), ("帮助", "有什么可以帮你的吗？"),
            ("天气", "今天天气不错呢"), ("时间", "现在是%时间yyyy年MM月dd日HH时mm分ss秒%"),
            ("签到", "签到成功！今天是第%随机数1-365%天"), ("菜单", "这里是功能菜单...")
        ]
        
        templates = self.question_templates[0]['templates']
        
        for i in range(count):
            trigger, response = random.choice(qa_pairs)
            template = random.choice(templates)
            
            input_text = template.format(trigger=trigger, response=response)
            
            think_text = f"用户需要一个基础的问答代码。词汇头应该是"{trigger}"，用于匹配用户输入。词汇体是回复内容"{response}"。结构非常简单，就是头和体的组合。"
            
            output_text = f"```secluded\n{trigger}\n{response}\n```"
            
            data.append(DatasetItem(input_text, think_text, output_text))
        
        return data

    def generate_variable_usage_data(self, count: int = 30) -> List[DatasetItem]:
        """生成变量使用数据"""
        data = []

        variable_questions = [
            {
                'input': '怎么在回复中显示用户的QQ号？',
                'think': '用户想要在回复中显示发送消息的用户QQ号。需要使用%QQ%变量来获取操作者的QQ号码。',
                'output': '```secluded\n查看QQ\n你的QQ号是：%QQ%\n```'
            },
            {
                'input': '如何获取当前时间？',
                'think': '用户需要获取当前时间信息。可以使用时间相关变量，如%时间yyyy年MM月dd日HH时mm分ss秒%来格式化显示时间。',
                'output': '```secluded\n时间\n当前时间：%时间yyyy年MM月dd日HH时mm分ss秒%\n```'
            },
            {
                'input': '怎样在消息中包含用户昵称？',
                'think': '用户想要在回复中显示发送消息用户的昵称。使用%昵称%或%UinName%变量可以获取操作者的昵称。',
                'output': '```secluded\n我的昵称\n你好，%昵称%！\n```'
            },
            {
                'input': '如何显示群号信息？',
                'think': '用户需要获取当前群聊的群号。使用%群号%或%GroupId%变量可以获取消息所在群聊的号码。',
                'output': '```secluded\n群信息\n当前群号：%群号%\n```'
            },
            {
                'input': '怎么获取随机数？',
                'think': '用户想要生成随机数。可以使用%随机数%变量生成随机数，或者%随机数最小值-最大值%指定范围。',
                'output': '```secluded\n随机数\n随机数：%随机数1-100%\n```'
            }
        ]

        for item in variable_questions:
            data.append(DatasetItem(item['input'], item['think'], item['output']))

        # 生成更多变体
        variables = self.syntax_knowledge['variables']
        for i in range(count - len(variable_questions)):
            var = random.choice(variables)

            input_text = f"如何使用{var['name']}变量？"
            think_text = f"用户询问{var['name']}变量的使用方法。{var['desc']}，使用{var['example']}来获取。"
            output_text = f"```secluded\n查看{var['name']}\n{var['desc']}：{var['example']}\n```"

            data.append(DatasetItem(input_text, think_text, output_text))

        return data

    def generate_function_usage_data(self, count: int = 40) -> List[DatasetItem]:
        """生成函数使用数据"""
        data = []

        function_examples = [
            {
                'input': '怎么发送图片？',
                'think': '用户想要发送图片。使用$图片 路径/链接$函数可以发送图片，支持本地路径和网络链接。',
                'output': '```secluded\n发图片\n$图片 https://example.com/image.jpg$\n```'
            },
            {
                'input': '如何发送语音消息？',
                'think': '用户需要发送语音。使用$语音 时长 路径$函数，需要指定语音时长（秒）和文件路径。',
                'output': '```secluded\n发语音\n$语音 10 /path/to/audio.mp3$\n```'
            },
            {
                'input': '怎样发起HTTP请求？',
                'think': '用户想要进行HTTP请求。使用$访问 链接$函数可以发起GET请求，返回响应内容。',
                'output': '```secluded\n网络请求\n结果：$访问 https://api.example.com$\n```'
            },
            {
                'input': '如何实现延时回复？',
                'think': '用户需要延时执行。使用$休眠 毫秒$函数可以暂停执行指定时间，单位是毫秒。',
                'output': '```secluded\n延时回复\n稍等一下...\n$休眠 3000$\n好了！\n```'
            },
            {
                'input': '怎么创建自定义变量？',
                'think': '用户想要创建变量。使用$变量 键 值$函数可以创建自定义变量，然后用%键%来使用。',
                'output': '```secluded\n创建变量\n$变量 用户名 张三$\n你好，%用户名%！\n```'
            }
        ]

        for item in function_examples:
            data.append(DatasetItem(item['input'], item['think'], item['output']))

        # 生成更多函数示例
        functions = self.syntax_knowledge['functions']
        for i in range(count - len(function_examples)):
            func = random.choice(functions)

            input_text = f"如何使用{func['name']}函数？"
            think_text = f"用户询问{func['name']}函数的用法。{func['desc']}，语法是{func['syntax']}。"
            output_text = f"```secluded\n使用{func['name']}\n{func['syntax']}\n```"

            data.append(DatasetItem(input_text, think_text, output_text))

        return data

    def generate_syntax_explanation_data(self, count: int = 25) -> List[DatasetItem]:
        """生成语法解释数据"""
        data = []

        syntax_explanations = [
            {
                'input': 'Secluded的基本语法结构是什么？',
                'think': '用户询问Secluded的基本语法结构。需要解释词汇头、词汇体的概念，以及它们之间的关系。',
                'output': 'Secluded采用词汇头+词汇体的结构：\n\n```secluded\n词汇头（正则表达式）\n词汇体（回复内容）\n词汇体（可多行）\n\n// 空行表示词汇结束\n```\n\n- 词汇头：用正则表达式匹配用户输入\n- 词汇体：机器人的回复内容\n- 空行：分隔不同的词汇'
            },
            {
                'input': '词汇头和词汇体有什么区别？',
                'think': '用户想了解词汇头和词汇体的区别。词汇头是匹配规则，词汇体是回复内容。',
                'output': '词汇头和词汇体的区别：\n\n**词汇头**：\n- 第一行，用于匹配用户输入\n- 支持正则表达式\n- 决定何时触发这个词汇\n\n**词汇体**：\n- 词汇头下面的所有行\n- 机器人的回复内容\n- 可以包含变量和函数\n- 可以多行\n\n```secluded\n你好.*  // 词汇头：匹配"你好"开头的消息\n你好，%昵称%！  // 词汇体：回复内容\n今天天气不错呢~  // 词汇体：可以多行\n```'
            },
            {
                'input': '如何写注释？',
                'think': '用户询问注释的写法。Secluded支持三种注释方式：//、##、&&。',
                'output': 'Secluded支持三种注释方式：\n\n```secluded\n// 这是注释类型1（最快识别）\n## 这是注释类型2\n&& 这是注释类型3\n\n// 注释行在加载时会被忽略\n// 不会占用内存空间\n```\n\n注意：\n- 注释必须在行首\n- 注释行不会被加载到内存\n- 推荐使用 // 注释（识别最快）'
            }
        ]

        for item in syntax_explanations:
            data.append(DatasetItem(item['input'], item['think'], item['output']))

        return data

    def generate_error_correction_data(self, count: int = 30) -> List[DatasetItem]:
        """生成错误修正数据"""
        data = []

        for error_pattern in self.error_patterns:
            input_text = f"这段代码有什么问题：\n```secluded\n{error_pattern['error']}\n```"
            think_text = f"用户提供的代码存在问题：{error_pattern['issue']}。需要指出错误并提供正确的写法。"
            output_text = f"代码问题：{error_pattern['issue']}\n\n正确写法：\n```secluded\n{error_pattern['correct']}\n```"

            data.append(DatasetItem(input_text, think_text, output_text))

        # 生成更多错误示例
        common_errors = [
            {
                'error': '测试\n\n好的',
                'issue': '词汇头和词汇体之间不能有空行',
                'correct': '测试\n好的'
            },
            {
                'error': '$图片 路径\n发送完成',
                'issue': '函数缺少结束符$',
                'correct': '$图片 路径$\n发送完成'
            },
            {
                'error': '%昵称\n你好%昵称%',
                'issue': '变量语法错误，缺少结束符%',
                'correct': '%昵称%\n你好%昵称%'
            },
            {
                'error': 'if:1==1\n正确\nelse\n错误\n\n其他内容',
                'issue': '条件语句后面不应该有空行',
                'correct': 'if:1==1\n正确\nelse\n错误'
            }
        ]

        for error in common_errors:
            input_text = f"帮我修复这个语法错误：\n```secluded\n{error['error']}\n```"
            think_text = f"代码存在语法错误：{error['issue']}。需要修正语法使其符合Secluded规范。"
            output_text = f"错误原因：{error['issue']}\n\n修复后的代码：\n```secluded\n{error['correct']}\n```"

            data.append(DatasetItem(input_text, think_text, output_text))

        return data

    def generate_complete_feature_data(self, count: int = 20) -> List[DatasetItem]:
        """生成完整功能数据"""
        data = []

        complete_features = [
            {
                'input': '写一个完整的签到功能',
                'think': '用户需要一个签到功能。应该包括签到命令、记录签到状态、显示签到信息等。可以使用配置文件存储签到数据。',
                'output': '''```secluded
签到
如果:$配置文件 签到.txt 存在 %QQ%$
今天已经签到过了！
连续签到：$配置文件 签到.txt 读取 %QQ%$天
返回
如果尾
$配置文件 签到.txt 写入 %QQ% 1$
签到成功！%昵称%
今天是你的第1天签到
获得积分：10分

查看签到
如果:$配置文件 签到.txt 存在 %QQ%$
%昵称%，你已连续签到：$配置文件 签到.txt 读取 %QQ%$天
返回
如果尾
你还没有签到过，发送"签到"开始吧！
```'''
            },
            {
                'input': '创建一个天气查询词库',
                'think': '用户需要天气查询功能。需要解析城市名称，调用天气API，格式化显示天气信息。',
                'output': '''```secluded
天气 (.*)
城市:$替换 %括号1% 市 $
如果:$字符长度 %城市%$<1
请输入城市名称，如：天气 北京
返回
如果尾
天气API:https://api.weather.com/v1/current?city=%城市%
结果:$访问 %天气API%$
如果:$字符长度 %结果%$<10
查询失败，请检查城市名称
返回
如果尾
%城市%天气：
%结果%

天气
请输入城市名称
例如：天气 北京
```'''
            },
            {
                'input': '做一个简单的聊天机器人',
                'think': '用户想要一个聊天机器人。需要包含多种对话场景，使用变量让回复更自然，添加一些智能回复。',
                'output': '''```secluded
你好.*
你好%昵称%！很高兴见到你~
今天过得怎么样？

再见.*
再见%昵称%！
祝你有美好的一天~

谢谢.*
不客气呢%昵称%！
能帮到你我很开心~

.*怎么样.*
听起来不错呢！
你觉得呢？

.*好无聊.*
那我们聊聊天吧！
你喜欢什么？

.*
我听到了：%MSG%
但是我不太明白，能换个说法吗？
```'''
            }
        ]

        for item in complete_features:
            data.append(DatasetItem(item['input'], item['think'], item['output']))

        return data

    def generate_multi_turn_data(self, count: int = 15) -> List[DatasetItem]:
        """生成多轮对话数据"""
        data = []

        multi_turn_examples = [
            {
                'input': '我想创建一个多步骤的用户注册流程，先询问用户名，再询问年龄，最后确认信息',
                'think': '用户需要多步骤交互流程。需要使用变量存储中间状态，通过不同的词汇头处理不同步骤，实现状态管理。',
                'output': '''```secluded
注册
请输入你的用户名：
$变量 注册状态_%QQ% 等待用户名$

注册用户名 (.*)
如果:$取变量 注册状态_%QQ%$==等待用户名
$变量 用户名_%QQ% %括号1%$
$变量 注册状态_%QQ% 等待年龄$
用户名：%括号1%
请输入你的年龄：
返回
如果尾
请先发送"注册"开始注册流程

注册年龄 ([0-9]+)
如果:$取变量 注册状态_%QQ%$==等待年龄
$变量 年龄_%QQ% %括号1%$
$变量 注册状态_%QQ% 确认信息$
注册信息确认：
用户名：$取变量 用户名_%QQ%$
年龄：%括号1%岁
发送"确认注册"完成注册
返回
如果尾
请先输入用户名

确认注册
如果:$取变量 注册状态_%QQ%$==确认信息
注册成功！
欢迎你，$取变量 用户名_%QQ%$！
$变量 注册状态_%QQ% 已完成$
返回
如果尾
请先完成前面的步骤
```'''
            }
        ]

        for item in multi_turn_examples:
            data.append(DatasetItem(item['input'], item['think'], item['output']))

        return data

    def generate_dataset(self, total_count: int = 200) -> List[DatasetItem]:
        """生成完整数据集"""
        all_data = []

        # 按比例生成不同类型的数据
        all_data.extend(self.generate_basic_qa_data(int(total_count * 0.25)))
        all_data.extend(self.generate_variable_usage_data(int(total_count * 0.15)))
        all_data.extend(self.generate_function_usage_data(int(total_count * 0.20)))
        all_data.extend(self.generate_syntax_explanation_data(int(total_count * 0.15)))
        all_data.extend(self.generate_error_correction_data(int(total_count * 0.15)))
        all_data.extend(self.generate_complete_feature_data(int(total_count * 0.07)))
        all_data.extend(self.generate_multi_turn_data(int(total_count * 0.03)))

        # 随机打乱
        random.shuffle(all_data)

        return all_data[:total_count]

    def save_dataset(self, data: List[DatasetItem], filename: str = "secluded_dataset.jsonl"):
        """保存数据集为JSONL格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            for item in data:
                json_item = {
                    "input": item.input,
                    "think": item.think,
                    "output": item.output
                }
                f.write(json.dumps(json_item, ensure_ascii=False) + '\n')

        print(f"数据集已保存到 {filename}，共 {len(data)} 条数据")

def main():
    """主函数"""
    generator = SecludedDatasetGenerator()

    # 生成数据集
    dataset = generator.generate_dataset(200)

    # 保存数据集
    generator.save_dataset(dataset, "secluded_training_dataset.jsonl")

    # 显示统计信息
    print(f"生成完成！")
    print(f"总数据量: {len(dataset)}")

    # 显示几个示例
    print("\n示例数据:")
    for i, item in enumerate(dataset[:3]):
        print(f"\n=== 示例 {i+1} ===")
        print(f"Input: {item.input}")
        print(f"Think: {item.think}")
        print(f"Output: {item.output}")

if __name__ == "__main__":
    main()
